#ifndef __UDP_H__
#define __UDP_H__
#include "stm32f4xx.h"
#include "Udp_Fun.h"
void udp_poll(void);
void upd_connect(void);
void network_init(void);
void force_use_default_network_config(void);
uint8_t test_w5500_connectivity(void);
void clear_flash_network_config(void);





extern uint8_t over_flag;
extern uint8_t gDATABUF[512];
extern uint8_t sDATABUF[512];

extern int16_t ret;
extern uint8_t DstIP[4];
extern uint16_t DstPort;
#endif
