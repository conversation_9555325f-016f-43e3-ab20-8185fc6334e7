#include "udp.h"
#include "spi.h"
#include "w5500.h"
#include "socket.h"	// Just include one header for WIZCHIP
#include "string.h"
#include "delay.h"
#include "stdio.h"
#include "serial_net_config.h"
#define SOCK_TCPS        0
#define DATA_BUF_SIZE   2048
uint8_t gDATABUF[512];
uint8_t sDATABUF[512]; //???????

uint8_t over_flag = 0;
wiz_NetInfo gWIZNETINFO = { .mac = {0x00, 0xDA, 0x70,0xA4, 0x48, 0xA8},
                            .ip = {192, 168, 10, 110},
                            .sn = {255,255,255,0},
                            .gw = {192, 168, 10, 121},
                            .dns = {8,8,8,8},
                            .dhcp = NETINFO_STATIC };
uint8_t tmp;
int16_t ret = 0;
uint8_t memsize[2][8] = {{2,2,2,2,2,2,2,2},{2,2,2,2,2,2,2,2}};
uint8_t DstIP[4]={192,168,10,130};
uint16_t	DstPort=6666;	//??????
uint16_t	Re_DstPort=6666;	//?????????
uint16_t	Local_DstPort=7777;	//?????????



void udp_poll(void)
{
	switch(getSn_SR(0))																						// ??socket0???
		{
			case SOCK_UDP:																							// Socket???????(??)??
					Delay_Ms(100);
					if(getSn_IR(0) & Sn_IR_RECV)
					{
						setSn_IR(0, Sn_IR_RECV);															// Sn_IR?RECV??1
					}
					// ????????:??????????W5500,W5500??????????????
					if((ret = getSn_RX_RSR(0))>0)	//??8??+?????
					{ 
						memset(gDATABUF,0,ret+1);
						recvfrom(0,gDATABUF, ret, DstIP,&Re_DstPort);			// W5500????????????,???SPI???MCU
						//printf("%s\r\n",gDATABUF);	
						//printf("%d\r\n",ret);						
						//sendto(0,gDATABUF,ret, DstIP, DstPort);		  		// ??????????????,??????

						Udp_status.Udp_Receive_Enable = Enable;
					}
			break;
			case SOCK_CLOSED:																						// Socket??????
					socket(0,Sn_MR_UDP,Local_DstPort,0x00);	//??3??????											// ??Socket0,????UDP??,????????
			break;
		}
}
void upd_connect(void)
{
	W5500_spi_init();
	w5500_init();
    Reset_W5500();
  	//uint16_t	DstPort=8080;
	reg_wizchip_cris_cbfunc(SPI_CrisEnter, SPI_CrisExit);	//???????
	/* Chip selection call back */
#if   _WIZCHIP_IO_MODE_ == _WIZCHIP_IO_MODE_SPI_VDM_
	reg_wizchip_cs_cbfunc(SPI_CS_Select, SPI_CS_Deselect);//??SPI??????
#elif _WIZCHIP_IO_MODE_ == _WIZCHIP_IO_MODE_SPI_FDM_
	reg_wizchip_cs_cbfunc(SPI_CS_Select, SPI_CS_Deselect);  // CS must be tried with LOW.
#else
   #if (_WIZCHIP_IO_MODE_ & _WIZCHIP_IO_MODE_SIP_) != _WIZCHIP_IO_MODE_SIP_
      #error "Unknown _WIZCHIP_IO_MODE_"
   #else
      reg_wizchip_cs_cbfunc(wizchip_select, wizchip_deselect);
   #endif
#endif
	/* SPI Read & Write callback function */
	reg_wizchip_spi_cbfunc(SPI_ReadByte, SPI_WriteByte);	//??????
	/* WIZCHIP SOCKET Buffer initialize */
//	if(ctlwizchip(CW_INIT_WIZCHIP,(void*)memsize) == -1){
//		 printf("WIZCHIP Initialized fail.\r\n");
//		 while(1);
//	}

//	/* PHY link status check */
//	do{
//		 if(ctlwizchip(CW_GET_PHYLINK, (void*)&tmp) == -1){
//				printf("Unknown PHY Link stauts.\r\n");
//		 }
//	}while(tmp == PHY_LINK_OFF);

	/* Network initialization */
	network_init();
}
void network_init(void)
{
    uint8_t tmpstr[6];
    int ret;

    // ???Flash??????
    NetConfig_t flash_config;
    NetConfigError_t config_result = load_network_config(&flash_config);

    if (config_result == NET_CONFIG_OK) {
        // ??Flash????
        printf("Loading network config from Flash...\n");
        memcpy(gWIZNETINFO.mac, flash_config.mac, 6);
        memcpy(gWIZNETINFO.ip, flash_config.ip, 4);
        memcpy(gWIZNETINFO.sn, flash_config.subnet, 4);
        memcpy(gWIZNETINFO.gw, flash_config.gateway, 4);
        memcpy(gWIZNETINFO.dns, flash_config.dns, 4);
        gWIZNETINFO.dhcp = flash_config.dhcp_mode ? NETINFO_DHCP : NETINFO_STATIC;

        printf("Flash config loaded: IP=%d.%d.%d.%d\n",
               flash_config.ip[0], flash_config.ip[1],
               flash_config.ip[2], flash_config.ip[3]);
    } else {
        // ??????
        printf("Using default network config (Flash load failed: %d)\n", config_result);
    }

    // ?????W5500
    ret = ctlnetwork(CN_SET_NETINFO, (void*)&gWIZNETINFO);
    printf("Set network info ret=%d\n",ret);

    // ????
    ret = ctlnetwork(CN_GET_NETINFO, (void*)&gWIZNETINFO);
    printf("Get network info ret=%d\n",ret);

    // ??????
    printf("Current W5500 config: IP=%d.%d.%d.%d, GW=%d.%d.%d.%d\n",
           gWIZNETINFO.ip[0], gWIZNETINFO.ip[1], gWIZNETINFO.ip[2], gWIZNETINFO.ip[3],
           gWIZNETINFO.gw[0], gWIZNETINFO.gw[1], gWIZNETINFO.gw[2], gWIZNETINFO.gw[3]);

	// Display Network Information
	ctlwizchip(CW_GET_ID,(void*)tmpstr);
	printf("\r\n=== %s NET CONF ===\r\n",(char*)tmpstr);
	printf("MAC: %02X:%02X:%02X:%02X:%02X:%02X\r\n",gWIZNETINFO.mac[0],gWIZNETINFO.mac[1],gWIZNETINFO.mac[2],
		  gWIZNETINFO.mac[3],gWIZNETINFO.mac[4],gWIZNETINFO.mac[5]);
	printf("SIP: %d.%d.%d.%d\r\n", gWIZNETINFO.ip[0],gWIZNETINFO.ip[1],gWIZNETINFO.ip[2],gWIZNETINFO.ip[3]);
	printf("GAR: %d.%d.%d.%d\r\n", gWIZNETINFO.gw[0],gWIZNETINFO.gw[1],gWIZNETINFO.gw[2],gWIZNETINFO.gw[3]);
	printf("SUB: %d.%d.%d.%d\r\n", gWIZNETINFO.sn[0],gWIZNETINFO.sn[1],gWIZNETINFO.sn[2],gWIZNETINFO.sn[3]);
	printf("DNS: %d.%d.%d.%d\r\n", gWIZNETINFO.dns[0],gWIZNETINFO.dns[1],gWIZNETINFO.dns[2],gWIZNETINFO.dns[3]);
	printf("======================\r\n");
}
